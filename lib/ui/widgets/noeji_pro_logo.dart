import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';

/// Helper widget to display the Noeji PRO logo using split SVGs
class NoejiProLogo extends StatelessWidget {
  /// Height of the logo
  final double height;
  
  /// Constructor
  const NoejiProLogo({
    super.key,
    required this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SvgPicture.asset(
          'assets/images/noeji_logo_v3.svg', // Just "NOEJI"
          height: height,
          colorFilter: ColorFilter.mode(
            NoejiTheme.colorsOf(context).textPrimary,
            BlendMode.srcIn,
          ),
        ),
        const SizedBox(width: 4), // Add spacing between "NOEJI" and "PRO"
        SvgPicture.asset(
          'assets/images/noeji_pro_logo.svg', // The "PRO" part with its own colors
          height: height * 0.8, // Slightly smaller as mentioned by user (16 vs 20)
          // NO colorFilter here, so it uses its intrinsic colors (white PRO on black box)
        ),
      ],
    );
  }
}
